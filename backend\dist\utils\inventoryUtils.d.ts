export declare function checkInventoryAlerts(itemType: 'material' | 'product', itemId: number): Promise<void>;
export declare function resolveInventoryAlerts(itemType: 'material' | 'product', itemId: number): Promise<void>;
export declare function checkAllInventoryAlerts(): Promise<void>;
export declare function updateInventoryWithMovement(itemType: 'material' | 'product', itemId: number, quantity: number, movementType: 'in' | 'out' | 'adjust', referenceType?: string, referenceId?: number, referenceNo?: string, remark?: string, createdBy?: number): Promise<void>;
//# sourceMappingURL=inventoryUtils.d.ts.map