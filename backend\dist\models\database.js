"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getDatabase = getDatabase;
exports.initDatabase = initDatabase;
exports.closeDatabase = closeDatabase;
const sqlite3_1 = __importDefault(require("sqlite3"));
const path_1 = __importDefault(require("path"));
// 数据库实例
let db;
// 获取数据库实例
function getDatabase() {
    if (!db) {
        throw new Error('数据库未初始化');
    }
    return db;
}
// 初始化数据库
async function initDatabase() {
    return new Promise((resolve, reject) => {
        const dbPath = path_1.default.join(__dirname, '../../data/erp.db');
        // 确保数据目录存在
        const fs = require('fs');
        const dataDir = path_1.default.dirname(dbPath);
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
        }
        db = new sqlite3_1.default.Database(dbPath, (err) => {
            if (err) {
                reject(err);
                return;
            }
            console.log('连接到SQLite数据库');
            // 设置UTF-8编码
            db.run("PRAGMA encoding = 'UTF-8'", (err) => {
                if (err) {
                    console.error('设置编码失败:', err);
                }
            });
            createTables()
                .then(() => resolve())
                .catch(reject);
        });
    });
}
// 创建数据表
async function createTables() {
    return new Promise((resolve, reject) => {
        db.serialize(() => {
            // 用户表
            db.run(`
        CREATE TABLE IF NOT EXISTS users (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          username VARCHAR(50) UNIQUE NOT NULL,
          password VARCHAR(255) NOT NULL,
          email VARCHAR(100),
          role VARCHAR(20) DEFAULT 'user',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);
            // 原材料表
            db.run(`
        CREATE TABLE IF NOT EXISTS materials (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          code VARCHAR(50) UNIQUE NOT NULL,
          name VARCHAR(100) NOT NULL,
          specification VARCHAR(200),
          unit VARCHAR(20) NOT NULL,
          cost_price DECIMAL(10,2) DEFAULT 0,
          stock_min INTEGER DEFAULT 0,
          stock_max INTEGER DEFAULT 0,
          current_stock INTEGER DEFAULT 0,
          status VARCHAR(20) DEFAULT 'active',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);
            // 成品表
            db.run(`
        CREATE TABLE IF NOT EXISTS products (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          code VARCHAR(50) UNIQUE NOT NULL,
          name VARCHAR(100) NOT NULL,
          specification VARCHAR(200),
          unit VARCHAR(20) NOT NULL,
          cost_price DECIMAL(10,2) DEFAULT 0,
          sale_price DECIMAL(10,2) DEFAULT 0,
          stock_min INTEGER DEFAULT 0,
          stock_max INTEGER DEFAULT 0,
          current_stock INTEGER DEFAULT 0,
          status VARCHAR(20) DEFAULT 'active',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);
            // 供应商表
            db.run(`
        CREATE TABLE IF NOT EXISTS suppliers (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          code VARCHAR(50) UNIQUE NOT NULL,
          name VARCHAR(100) NOT NULL,
          contact_person VARCHAR(50),
          phone VARCHAR(20),
          address VARCHAR(200),
          settlement_method VARCHAR(50),
          status VARCHAR(20) DEFAULT 'active',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);
            // 客户表
            db.run(`
        CREATE TABLE IF NOT EXISTS customers (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          code VARCHAR(50) UNIQUE NOT NULL,
          name VARCHAR(100) NOT NULL,
          contact_person VARCHAR(50),
          phone VARCHAR(20),
          address VARCHAR(200),
          credit_limit DECIMAL(10,2) DEFAULT 0,
          status VARCHAR(20) DEFAULT 'active',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);
            // 采购订单表
            db.run(`
        CREATE TABLE IF NOT EXISTS purchase_orders (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          order_no VARCHAR(50) UNIQUE NOT NULL,
          supplier_id INTEGER NOT NULL,
          order_date DATE NOT NULL,
          expected_date DATE,
          total_amount DECIMAL(10,2) DEFAULT 0,
          status VARCHAR(20) DEFAULT 'draft',
          remark TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (supplier_id) REFERENCES suppliers(id)
        )
      `);
            // 采购订单明细表
            db.run(`
        CREATE TABLE IF NOT EXISTS purchase_order_items (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          purchase_order_id INTEGER NOT NULL,
          material_id INTEGER NOT NULL,
          quantity DECIMAL(10,2) NOT NULL,
          unit_price DECIMAL(10,2) NOT NULL,
          total_price DECIMAL(10,2) NOT NULL,
          received_quantity DECIMAL(10,2) DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (purchase_order_id) REFERENCES purchase_orders(id),
          FOREIGN KEY (material_id) REFERENCES materials(id)
        )
      `);
            // 采购入库单表
            db.run(`
        CREATE TABLE IF NOT EXISTS purchase_receipts (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          receipt_no VARCHAR(50) UNIQUE NOT NULL,
          purchase_order_id INTEGER NOT NULL,
          supplier_id INTEGER NOT NULL,
          receipt_date DATE NOT NULL,
          total_amount DECIMAL(10,2) DEFAULT 0,
          status VARCHAR(20) DEFAULT 'draft',
          remark TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (purchase_order_id) REFERENCES purchase_orders(id),
          FOREIGN KEY (supplier_id) REFERENCES suppliers(id)
        )
      `);
            // 采购入库单明细表
            db.run(`
        CREATE TABLE IF NOT EXISTS purchase_receipt_items (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          purchase_receipt_id INTEGER NOT NULL,
          material_id INTEGER NOT NULL,
          quantity DECIMAL(10,2) NOT NULL,
          unit_price DECIMAL(10,2) NOT NULL,
          total_price DECIMAL(10,2) NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (purchase_receipt_id) REFERENCES purchase_receipts(id),
          FOREIGN KEY (material_id) REFERENCES materials(id)
        )
      `);
            // 销售订单表
            db.run(`
        CREATE TABLE IF NOT EXISTS sales_orders (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          order_no VARCHAR(50) UNIQUE NOT NULL,
          customer_id INTEGER NOT NULL,
          order_date DATE NOT NULL,
          delivery_date DATE,
          total_amount DECIMAL(10,2) DEFAULT 0,
          status VARCHAR(20) DEFAULT 'draft',
          remark TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (customer_id) REFERENCES customers(id)
        )
      `);
            // 销售订单明细表
            db.run(`
        CREATE TABLE IF NOT EXISTS sales_order_items (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          sales_order_id INTEGER NOT NULL,
          product_id INTEGER NOT NULL,
          quantity DECIMAL(10,2) NOT NULL,
          unit_price DECIMAL(10,2) NOT NULL,
          total_price DECIMAL(10,2) NOT NULL,
          delivered_quantity DECIMAL(10,2) DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (sales_order_id) REFERENCES sales_orders(id),
          FOREIGN KEY (product_id) REFERENCES products(id)
        )
      `);
            // 销售出库单表
            db.run(`
        CREATE TABLE IF NOT EXISTS sales_deliveries (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          delivery_no VARCHAR(50) UNIQUE NOT NULL,
          sales_order_id INTEGER NOT NULL,
          customer_id INTEGER NOT NULL,
          delivery_date DATE NOT NULL,
          total_amount DECIMAL(10,2) DEFAULT 0,
          status VARCHAR(20) DEFAULT 'draft',
          remark TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (sales_order_id) REFERENCES sales_orders(id),
          FOREIGN KEY (customer_id) REFERENCES customers(id)
        )
      `);
            // 销售出库单明细表
            db.run(`
        CREATE TABLE IF NOT EXISTS sales_delivery_items (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          sales_delivery_id INTEGER NOT NULL,
          product_id INTEGER NOT NULL,
          quantity DECIMAL(10,2) NOT NULL,
          unit_price DECIMAL(10,2) NOT NULL,
          total_price DECIMAL(10,2) NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (sales_delivery_id) REFERENCES sales_deliveries(id),
          FOREIGN KEY (product_id) REFERENCES products(id)
        )
      `);
            // 生产计划表
            db.run(`
        CREATE TABLE IF NOT EXISTS production_plans (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          plan_no VARCHAR(50) UNIQUE NOT NULL,
          product_id INTEGER NOT NULL,
          planned_quantity DECIMAL(10,2) NOT NULL,
          actual_quantity DECIMAL(10,2) DEFAULT 0,
          plan_date DATE NOT NULL,
          start_date DATE,
          completion_date DATE,
          status VARCHAR(20) DEFAULT 'draft',
          remark TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (product_id) REFERENCES products(id)
        )
      `);
            // 生产计划物料明细表
            db.run(`
        CREATE TABLE IF NOT EXISTS production_plan_items (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          production_plan_id INTEGER NOT NULL,
          material_id INTEGER NOT NULL,
          required_quantity DECIMAL(10,2) NOT NULL,
          consumed_quantity DECIMAL(10,2) DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (production_plan_id) REFERENCES production_plans(id),
          FOREIGN KEY (material_id) REFERENCES materials(id)
        )
      `);
            // 生产完工表
            db.run(`
        CREATE TABLE IF NOT EXISTS production_completions (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          completion_no VARCHAR(50) UNIQUE NOT NULL,
          production_plan_id INTEGER NOT NULL,
          product_id INTEGER NOT NULL,
          completed_quantity DECIMAL(10,2) NOT NULL,
          completion_date DATE NOT NULL,
          status VARCHAR(20) DEFAULT 'draft',
          remark TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (production_plan_id) REFERENCES production_plans(id),
          FOREIGN KEY (product_id) REFERENCES products(id)
        )
      `);
            // 生产完工物料消耗明细表
            db.run(`
        CREATE TABLE IF NOT EXISTS production_completion_items (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          production_completion_id INTEGER NOT NULL,
          material_id INTEGER NOT NULL,
          consumed_quantity DECIMAL(10,2) NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (production_completion_id) REFERENCES production_completions(id),
          FOREIGN KEY (material_id) REFERENCES materials(id)
        )
      `);
            // 库存变动记录表
            db.run(`
        CREATE TABLE IF NOT EXISTS inventory_movements (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          item_type VARCHAR(20) NOT NULL, -- 'material' 或 'product'
          item_id INTEGER NOT NULL,
          movement_type VARCHAR(20) NOT NULL, -- 'in' 入库, 'out' 出库, 'adjust' 调整
          quantity DECIMAL(10,2) NOT NULL,
          before_quantity DECIMAL(10,2) NOT NULL,
          after_quantity DECIMAL(10,2) NOT NULL,
          reference_type VARCHAR(50), -- 关联单据类型：purchase_receipt, sales_delivery, production_completion, stocktaking
          reference_id INTEGER, -- 关联单据ID
          reference_no VARCHAR(50), -- 关联单据号
          remark TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          created_by INTEGER,
          FOREIGN KEY (created_by) REFERENCES users(id)
        )
      `);
            // 库存预警记录表
            db.run(`
        CREATE TABLE IF NOT EXISTS inventory_alerts (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          item_type VARCHAR(20) NOT NULL, -- 'material' 或 'product'
          item_id INTEGER NOT NULL,
          alert_type VARCHAR(20) NOT NULL, -- 'low_stock' 低库存, 'high_stock' 高库存, 'zero_stock' 零库存
          current_stock DECIMAL(10,2) NOT NULL,
          threshold_value DECIMAL(10,2), -- 触发阈值
          status VARCHAR(20) DEFAULT 'active', -- 'active' 活跃, 'resolved' 已解决
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          resolved_at DATETIME,
          resolved_by INTEGER,
          FOREIGN KEY (resolved_by) REFERENCES users(id)
        )
      `);
            // 库存盘点任务表
            db.run(`
        CREATE TABLE IF NOT EXISTS stocktaking_tasks (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          task_no VARCHAR(50) UNIQUE NOT NULL,
          title VARCHAR(100) NOT NULL,
          description TEXT,
          task_date DATE NOT NULL,
          status VARCHAR(20) DEFAULT 'draft', -- 'draft' 草稿, 'in_progress' 进行中, 'completed' 已完成, 'cancelled' 已取消
          created_by INTEGER NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          completed_at DATETIME,
          FOREIGN KEY (created_by) REFERENCES users(id)
        )
      `);
            // 库存盘点明细表
            db.run(`
        CREATE TABLE IF NOT EXISTS stocktaking_items (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          stocktaking_task_id INTEGER NOT NULL,
          item_type VARCHAR(20) NOT NULL, -- 'material' 或 'product'
          item_id INTEGER NOT NULL,
          system_quantity DECIMAL(10,2) NOT NULL, -- 系统库存数量
          actual_quantity DECIMAL(10,2), -- 实际盘点数量
          difference_quantity DECIMAL(10,2), -- 差异数量
          status VARCHAR(20) DEFAULT 'pending', -- 'pending' 待盘点, 'counted' 已盘点, 'adjusted' 已调整
          remark TEXT,
          counted_at DATETIME,
          counted_by INTEGER,
          adjusted_at DATETIME,
          adjusted_by INTEGER,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (stocktaking_task_id) REFERENCES stocktaking_tasks(id),
          FOREIGN KEY (counted_by) REFERENCES users(id),
          FOREIGN KEY (adjusted_by) REFERENCES users(id)
        )
      `);
            // 创建索引以优化查询性能
            db.run(`CREATE INDEX IF NOT EXISTS idx_inventory_movements_item ON inventory_movements(item_type, item_id)`);
            db.run(`CREATE INDEX IF NOT EXISTS idx_inventory_movements_created_at ON inventory_movements(created_at)`);
            db.run(`CREATE INDEX IF NOT EXISTS idx_inventory_movements_reference ON inventory_movements(reference_type, reference_id)`);
            db.run(`CREATE INDEX IF NOT EXISTS idx_inventory_alerts_item ON inventory_alerts(item_type, item_id)`);
            db.run(`CREATE INDEX IF NOT EXISTS idx_inventory_alerts_status ON inventory_alerts(status)`);
            db.run(`CREATE INDEX IF NOT EXISTS idx_inventory_alerts_created_at ON inventory_alerts(created_at)`);
            db.run(`CREATE INDEX IF NOT EXISTS idx_stocktaking_tasks_status ON stocktaking_tasks(status)`);
            db.run(`CREATE INDEX IF NOT EXISTS idx_stocktaking_tasks_created_at ON stocktaking_tasks(created_at)`);
            db.run(`CREATE INDEX IF NOT EXISTS idx_stocktaking_items_task ON stocktaking_items(stocktaking_task_id)`);
            db.run(`CREATE INDEX IF NOT EXISTS idx_stocktaking_items_item ON stocktaking_items(item_type, item_id)`);
            db.run(`CREATE INDEX IF NOT EXISTS idx_stocktaking_items_status ON stocktaking_items(status)`);
            // 为现有表添加索引
            db.run(`CREATE INDEX IF NOT EXISTS idx_purchase_receipts_date ON purchase_receipts(receipt_date)`);
            db.run(`CREATE INDEX IF NOT EXISTS idx_sales_deliveries_date ON sales_deliveries(delivery_date)`);
            db.run(`CREATE INDEX IF NOT EXISTS idx_production_completions_date ON production_completions(completion_date)`);
            db.run(`CREATE INDEX IF NOT EXISTS idx_purchase_receipt_items_material ON purchase_receipt_items(material_id)`);
            db.run(`CREATE INDEX IF NOT EXISTS idx_sales_delivery_items_product ON sales_delivery_items(product_id)`);
            db.run(`CREATE INDEX IF NOT EXISTS idx_production_completion_items_material ON production_completion_items(material_id)`);
            // 为库存查询优化添加索引
            db.run(`CREATE INDEX IF NOT EXISTS idx_materials_status_code ON materials(status, code)`);
            db.run(`CREATE INDEX IF NOT EXISTS idx_materials_status_name ON materials(status, name)`);
            db.run(`CREATE INDEX IF NOT EXISTS idx_materials_current_stock ON materials(current_stock)`);
            db.run(`CREATE INDEX IF NOT EXISTS idx_materials_stock_levels ON materials(stock_min, stock_max, current_stock)`);
            db.run(`CREATE INDEX IF NOT EXISTS idx_products_status_code ON products(status, code)`);
            db.run(`CREATE INDEX IF NOT EXISTS idx_products_status_name ON products(status, name)`);
            db.run(`CREATE INDEX IF NOT EXISTS idx_products_current_stock ON products(current_stock)`);
            db.run(`CREATE INDEX IF NOT EXISTS idx_products_stock_levels ON products(stock_min, stock_max, current_stock)`);
            console.log('数据表和索引创建成功');
            createDefaultUser().then(() => {
                resolve();
            }).catch((error) => {
                reject(error);
            });
        });
    });
}
// 创建默认用户
async function createDefaultUser() {
    return new Promise((resolve, reject) => {
        // 检查是否已存在admin用户
        db.get('SELECT * FROM users WHERE username = ?', ['admin'], async (err, row) => {
            if (err) {
                reject(err);
                return;
            }
            if (!row) {
                // 如果不存在admin用户，则创建
                const bcrypt = require('bcryptjs');
                const hashedPassword = await bcrypt.hash('123456', 10);
                db.run('INSERT INTO users (username, password, email, role) VALUES (?, ?, ?, ?)', ['admin', hashedPassword, '<EMAIL>', 'admin'], (err) => {
                    if (err) {
                        reject(err);
                    }
                    else {
                        console.log('默认admin用户创建成功');
                        resolve();
                    }
                });
            }
            else {
                console.log('admin用户已存在');
                resolve();
            }
        });
    });
}
// 关闭数据库连接
function closeDatabase() {
    if (db) {
        db.close((err) => {
            if (err) {
                console.error('关闭数据库时出错:', err);
            }
            else {
                console.log('数据库连接已关闭');
            }
        });
    }
}
//# sourceMappingURL=database.js.map