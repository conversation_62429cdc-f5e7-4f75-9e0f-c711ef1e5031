import { Request, Response } from 'express';
export declare function getStocktakingTasks(req: Request, res: Response): Promise<void>;
export declare function getStocktakingTask(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
export declare function createStocktakingTask(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
export declare function updateStocktakingTask(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
export declare function updateStocktakingItem(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
export declare function getStocktakingAnalysis(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
export declare function adjustInventory(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
//# sourceMappingURL=stocktakingController.d.ts.map