{"version": 3, "file": "salesDeliveryController.js", "sourceRoot": "", "sources": ["../../src/controllers/salesDeliveryController.ts"], "names": [], "mappings": ";;AAsBA,gDA8EC;AAGD,4CAiEC;AAGD,kDAuJC;AAjUD,iDAAiD;AAQjD,4DAAsE;AAEtE,SAAS;AACT,SAAS,kBAAkB;IACzB,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;IACvB,MAAM,IAAI,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC;IAC/B,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IAC1D,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IACnD,MAAM,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU;IACxD,OAAO,KAAK,IAAI,GAAG,KAAK,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC;AAC1C,CAAC;AAED,YAAY;AACL,KAAK,UAAU,kBAAkB,CAAC,GAAY,EAAE,GAAa;IAClE,IAAI,CAAC;QACH,MAAM,EAAE,IAAI,GAAG,CAAC,EAAE,KAAK,GAAG,EAAE,EAAE,MAAM,GAAG,EAAE,EAAE,MAAM,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC;QACrE,MAAM,MAAM,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QAElD,MAAM,EAAE,GAAG,IAAA,sBAAW,GAAE,CAAC;QAEzB,SAAS;QACT,IAAI,WAAW,GAAG,WAAW,CAAC;QAC9B,MAAM,MAAM,GAAU,EAAE,CAAC;QAEzB,IAAI,MAAM,EAAE,CAAC;YACX,WAAW,IAAI,qEAAqE,CAAC;YACrF,MAAM,aAAa,GAAG,IAAI,MAAM,GAAG,CAAC;YACpC,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,MAAM,EAAE,CAAC;YACX,WAAW,IAAI,oBAAoB,CAAC;YACpC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACtB,CAAC;QAED,OAAO;QACP,MAAM,UAAU,GAAG;;;;;QAKf,WAAW;KACd,CAAC;QACF,MAAM,WAAW,GAAG,MAAM,IAAI,OAAO,CAAoB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC3E,EAAE,CAAC,GAAG,CAAC,UAAU,EAAE,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;gBACtC,IAAI,GAAG;oBAAE,MAAM,CAAC,GAAG,CAAC,CAAC;;oBAChB,OAAO,CAAC,GAAwB,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,OAAO;QACP,MAAM,SAAS,GAAG;;;;;;;;QAQd,WAAW;;;KAGd,CAAC;QACF,MAAM,UAAU,GAAG,MAAM,IAAI,OAAO,CAAkB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACxE,EAAE,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,MAAM,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;gBAClE,IAAI,GAAG;oBAAE,MAAM,CAAC,GAAG,CAAC,CAAC;;oBAChB,OAAO,CAAC,IAAuB,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,MAAM,QAAQ,GAAqC;YACjD,IAAI,EAAE,UAAU;YAChB,KAAK,EAAE,WAAW,CAAC,KAAK;YACxB,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC;YAClB,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC;YACpB,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;SACzD,CAAC;QAEF,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,aAAa;YACtB,IAAI,EAAE,QAAQ;SACkC,CAAC,CAAC;IAEtD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;QACrC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,SAAS;SACJ,CAAC,CAAC;IACpB,CAAC;AACH,CAAC;AAED,oBAAoB;AACb,KAAK,UAAU,gBAAgB,CAAC,GAAY,EAAE,GAAa;IAChE,IAAI,CAAC;QACH,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAC1B,MAAM,EAAE,GAAG,IAAA,sBAAW,GAAE,CAAC;QAEzB,WAAW;QACX,MAAM,QAAQ,GAAG,MAAM,IAAI,OAAO,CAA4B,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAChF,EAAE,CAAC,GAAG,CAAC;;;;;;;;;OASN,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;gBACpB,IAAI,GAAG;oBAAE,MAAM,CAAC,GAAG,CAAC,CAAC;;oBAChB,OAAO,CAAC,GAAoB,CAAC,CAAC;YACrC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,UAAU;aACL,CAAC,CAAC;QACpB,CAAC;QAED,UAAU;QACV,MAAM,KAAK,GAAG,MAAM,IAAI,OAAO,CAAsB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACvE,EAAE,CAAC,GAAG,CAAC;;;;;;;;;;OAUN,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;gBACrB,IAAI,GAAG;oBAAE,MAAM,CAAC,GAAG,CAAC,CAAC;;oBAChB,OAAO,CAAC,IAA2B,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,MAAM,iBAAiB,GAAG;YACxB,GAAG,QAAQ;YACX,KAAK;SACN,CAAC;QAEF,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,WAAW;YACpB,IAAI,EAAE,iBAAiB;SACT,CAAC,CAAC;IAEpB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;QACnC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,SAAS;SACJ,CAAC,CAAC;IACpB,CAAC;AACH,CAAC;AAED,UAAU;AACH,KAAK,UAAU,mBAAmB,CAAC,GAAY,EAAE,GAAa;IACnE,IAAI,CAAC;QACH,MAAM,EACJ,cAAc,EACd,aAAa,EACb,MAAM,GAAG,EAAE,EACX,KAAK,EACN,GAA6B,GAAG,CAAC,IAAI,CAAC;QAEvC,SAAS;QACT,IAAI,CAAC,cAAc,IAAI,CAAC,aAAa,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACtE,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,oBAAoB;aACf,CAAC,CAAC;QACpB,CAAC;QAED,MAAM,EAAE,GAAG,IAAA,sBAAW,GAAE,CAAC;QAEzB,iBAAiB;QACjB,MAAM,UAAU,GAAG,MAAM,IAAI,OAAO,CAAM,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC5D,EAAE,CAAC,GAAG,CAAC;;;;;OAKN,EAAE,CAAC,cAAc,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;gBAChC,IAAI,GAAG;oBAAE,MAAM,CAAC,GAAG,CAAC,CAAC;;oBAChB,OAAO,CAAC,GAAG,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,SAAS;aACJ,CAAC,CAAC;QACpB,CAAC;QAED,IAAI,UAAU,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;YACrC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,mBAAmB;aACd,CAAC,CAAC;QACpB,CAAC;QAED,aAAa;QACb,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,MAAM,OAAO,GAAG,MAAM,IAAI,OAAO,CAAM,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACzD,EAAE,CAAC,GAAG,CAAC,qCAAqC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;oBAC5E,IAAI,GAAG;wBAAE,MAAM,CAAC,GAAG,CAAC,CAAC;;wBAChB,OAAO,CAAC,GAAG,CAAC,CAAC;gBACpB,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,QAAQ,IAAI,CAAC,UAAU,MAAM;iBACxB,CAAC,CAAC;YACpB,CAAC;YAED,IAAI,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC1C,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,OAAO,OAAO,CAAC,IAAI,eAAe,OAAO,CAAC,aAAa,OAAO,IAAI,CAAC,QAAQ,EAAE;iBACxE,CAAC,CAAC;YACpB,CAAC;QACH,CAAC;QAED,MAAM,UAAU,GAAG,kBAAkB,EAAE,CAAC;QAExC,QAAQ;QACR,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;QAE5F,OAAO;QACP,MAAM,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC1C,EAAE,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;gBACtB,IAAI,CAAC;oBACH,EAAE,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;oBAE5B,YAAY;oBACZ,MAAM,UAAU,GAAG,MAAM,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;wBAC/D,EAAE,CAAC,GAAG,CAAC;;;;;aAKN,EAAE,CAAC,UAAU,EAAE,cAAc,EAAE,UAAU,CAAC,WAAW,EAAE,aAAa,EAAE,WAAW,EAAE,MAAM,CAAC,EAC3F,UAAS,GAAG;4BACV,IAAI,GAAG;gCAAE,MAAM,CAAC,GAAG,CAAC,CAAC;;gCAChB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;wBAC5B,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC;oBAEH,iBAAiB;oBACjB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;wBACzB,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC;wBAEnD,UAAU;wBACV,MAAM,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;4BAC1C,EAAE,CAAC,GAAG,CAAC;;;;eAIN,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,EAC5E,CAAC,GAAG,EAAE,EAAE;gCACN,IAAI,GAAG;oCAAE,MAAM,CAAC,GAAG,CAAC,CAAC;;oCAChB,OAAO,EAAE,CAAC;4BACjB,CAAC,CAAC,CAAC;wBACL,CAAC,CAAC,CAAC;wBAEH,kBAAkB;wBAClB,MAAM,IAAA,4CAA2B,EAC/B,SAAS,EACT,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,QAAQ,EACb,KAAK,EACL,gBAAgB,EAChB,UAAU,EACV,UAAU,EACV,QAAQ,IAAI,CAAC,QAAQ,EAAE,EACtB,GAAW,CAAC,IAAI,EAAE,EAAE,CACtB,CAAC;oBACJ,CAAC;oBAED,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,EAAE;wBACvB,IAAI,GAAG;4BAAE,MAAM,CAAC,GAAG,CAAC,CAAC;;4BAChB,OAAO,EAAE,CAAC;oBACjB,CAAC,CAAC,CAAC;gBAEL,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;oBACnB,MAAM,CAAC,KAAK,CAAC,CAAC;gBAChB,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,iBAAiB;YAC1B,IAAI,EAAE,EAAE,WAAW,EAAE,UAAU,EAAE;SACnB,CAAC,CAAC;IAEpB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;QACnC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,SAAS;SACJ,CAAC,CAAC;IACpB,CAAC;AACH,CAAC"}