"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const dotenv_1 = __importDefault(require("dotenv"));
const database_1 = require("./models/database");
const auth_1 = __importDefault(require("./routes/auth"));
const materials_1 = __importDefault(require("./routes/materials"));
const products_1 = __importDefault(require("./routes/products"));
const suppliers_1 = __importDefault(require("./routes/suppliers"));
const customers_1 = __importDefault(require("./routes/customers"));
const purchaseOrders_1 = __importDefault(require("./routes/purchaseOrders"));
const purchaseReceipts_1 = __importDefault(require("./routes/purchaseReceipts"));
const salesOrders_1 = __importDefault(require("./routes/salesOrders"));
const salesDeliveries_1 = __importDefault(require("./routes/salesDeliveries"));
const productionPlans_1 = __importDefault(require("./routes/productionPlans"));
const productionCompletions_1 = __importDefault(require("./routes/productionCompletions"));
const inventory_1 = __importDefault(require("./routes/inventory"));
const stocktaking_1 = __importDefault(require("./routes/stocktaking"));
const reports_1 = __importDefault(require("./routes/reports"));
// 加载环境变量
dotenv_1.default.config();
const app = (0, express_1.default)();
const PORT = process.env.PORT || 3000;
// 中间件
app.use((0, cors_1.default)());
app.use(express_1.default.json());
app.use(express_1.default.urlencoded({ extended: true }));
// 基础路由
app.get('/', (req, res) => {
    res.json({ message: 'ERP进销存管理系统 API 服务器运行中' });
});
// 健康检查
app.get('/health', (req, res) => {
    res.json({ status: 'OK', timestamp: new Date().toISOString() });
});
// API路由
app.use('/api/auth', auth_1.default);
app.use('/api/materials', materials_1.default);
app.use('/api/products', products_1.default);
app.use('/api/suppliers', suppliers_1.default);
app.use('/api/customers', customers_1.default);
app.use('/api/purchase-orders', purchaseOrders_1.default);
app.use('/api/purchase-receipts', purchaseReceipts_1.default);
app.use('/api/sales-orders', salesOrders_1.default);
app.use('/api/sales-deliveries', salesDeliveries_1.default);
app.use('/api/production-plans', productionPlans_1.default);
app.use('/api/production-completions', productionCompletions_1.default);
app.use('/api/inventory', inventory_1.default);
app.use('/api/stocktaking', stocktaking_1.default);
app.use('/api/reports', reports_1.default);
// 启动服务器
async function startServer() {
    try {
        // 初始化数据库
        await (0, database_1.initDatabase)();
        console.log('数据库初始化成功');
        app.listen(PORT, () => {
            console.log(`服务器运行在端口 ${PORT}`);
            console.log(`API地址: http://localhost:${PORT}`);
        });
    }
    catch (error) {
        console.error('服务器启动失败:', error);
        process.exit(1);
    }
}
startServer();
//# sourceMappingURL=index.js.map