import { Request, Response } from 'express';
import { InventoryMovementCreateInput, InventoryAlertCreateInput } from '../types';
export declare function getInventoryList(req: Request, res: Response): Promise<void>;
export declare function getMaterialsInventory(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
export declare function getProductsInventory(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
export declare function getInventoryHistory(req: Request, res: Response): Promise<void>;
export declare function getInventoryAlerts(req: Request, res: Response): Promise<void>;
export declare function createInventoryMovement(movementData: InventoryMovementCreateInput): Promise<number>;
export declare function createInventoryAlert(alertData: InventoryAlertCreateInput): Promise<number>;
//# sourceMappingURL=inventoryController.d.ts.map