"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const inventoryController_1 = require("../controllers/inventoryController");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
// 所有路由都需要认证
router.use(auth_1.authenticateToken);
// GET /api/inventory - 获取库存列表（原材料和成品）
router.get('/', inventoryController_1.getInventoryList);
// GET /api/inventory/materials - 获取原材料库存列表
router.get('/materials', inventoryController_1.getMaterialsInventory);
// GET /api/inventory/products - 获取成品库存列表
router.get('/products', inventoryController_1.getProductsInventory);
// GET /api/inventory/:itemType/:itemId/history - 获取库存变动历史
router.get('/:itemType/:itemId/history', inventoryController_1.getInventoryHistory);
// GET /api/inventory/alerts - 获取库存预警信息
router.get('/alerts', inventoryController_1.getInventoryAlerts);
exports.default = router;
//# sourceMappingURL=inventory.js.map