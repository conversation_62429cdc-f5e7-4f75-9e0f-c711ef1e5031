{"root": ["../../env.d.ts", "../../src/app.vue", "../../src/main.ts", "../../src/api/auth.ts", "../../src/api/customers.ts", "../../src/api/index.ts", "../../src/api/inventory.ts", "../../src/api/materials.ts", "../../src/api/productioncompletions.ts", "../../src/api/productionplans.ts", "../../src/api/products.ts", "../../src/api/purchaseorders.ts", "../../src/api/purchasereceipts.ts", "../../src/api/reports.ts", "../../src/api/salesdeliveries.ts", "../../src/api/salesorders.ts", "../../src/api/stocktaking.ts", "../../src/api/suppliers.ts", "../../src/components/datatable.vue", "../../src/components/echart.vue", "../../src/components/emptystate.vue", "../../src/components/formdialog.vue", "../../src/components/helloworld.vue", "../../src/components/loadingstate.vue", "../../src/components/thewelcome.vue", "../../src/components/welcomeitem.vue", "../../src/components/icons/iconcommunity.vue", "../../src/components/icons/icondocumentation.vue", "../../src/components/icons/iconecosystem.vue", "../../src/components/icons/iconsupport.vue", "../../src/components/icons/icontooling.vue", "../../src/composables/useloading.ts", "../../src/composables/usewindowsize.ts", "../../src/router/index.ts", "../../src/stores/auth.ts", "../../src/views/aboutview.vue", "../../src/views/customersview.vue", "../../src/views/homeview.vue", "../../src/views/inventoryalertsview.vue", "../../src/views/inventoryview.vue", "../../src/views/layoutview.vue", "../../src/views/loginview.vue", "../../src/views/materialsinventoryview.vue", "../../src/views/materialsview.vue", "../../src/views/productioncompletionsview.vue", "../../src/views/productionplansview.vue", "../../src/views/productsinventoryview.vue", "../../src/views/productsview.vue", "../../src/views/purchaseordersview.vue", "../../src/views/purchasereceiptsview.vue", "../../src/views/purchasereportview.vue", "../../src/views/reportsview.vue", "../../src/views/salesdeliveriesview.vue", "../../src/views/salesordersview.vue", "../../src/views/salesreportview.vue", "../../src/views/stocktakingdetailview.vue", "../../src/views/stocktakingview.vue", "../../src/views/suppliersview.vue"], "errors": true, "version": "5.8.3"}