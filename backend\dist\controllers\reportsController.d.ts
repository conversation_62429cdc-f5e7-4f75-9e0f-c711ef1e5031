import { Request, Response } from 'express';
export declare function getPurchaseSummaryReport(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
export declare function getSalesSummaryReport(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
export declare function getInventoryMovementReport(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
export declare function getProductionCostReport(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
export declare function getFinancialSummaryReport(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
//# sourceMappingURL=reportsController.d.ts.map