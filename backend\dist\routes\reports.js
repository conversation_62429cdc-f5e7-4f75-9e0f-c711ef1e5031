"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const reportsController_1 = require("../controllers/reportsController");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
// 所有路由都需要认证
router.use(auth_1.authenticateToken);
// GET /api/reports/purchase-summary - 获取采购汇总报表
router.get('/purchase-summary', reportsController_1.getPurchaseSummaryReport);
// GET /api/reports/sales-summary - 获取销售汇总报表
router.get('/sales-summary', reportsController_1.getSalesSummaryReport);
// GET /api/reports/inventory-movement - 获取库存变动报表
router.get('/inventory-movement', reportsController_1.getInventoryMovementReport);
// GET /api/reports/production-cost - 获取生产成本报表
router.get('/production-cost', reportsController_1.getProductionCostReport);
// GET /api/reports/financial-summary - 获取财务汇总报表
router.get('/financial-summary', reportsController_1.getFinancialSummaryReport);
exports.default = router;
//# sourceMappingURL=reports.js.map