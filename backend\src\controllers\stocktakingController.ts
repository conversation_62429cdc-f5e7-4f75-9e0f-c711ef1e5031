import { Request, Response } from 'express';
import { getDatabase } from '../models/database';
import { 
  StocktakingTask, 
  StocktakingTaskCreateInput,
  StocktakingTaskUpdateInput,
  StocktakingItem,
  StocktakingItemCreateInput,
  StocktakingItemUpdateInput,
  ApiResponse, 
  PaginatedResponse 
} from '../types';
import { updateInventoryWithMovement } from '../utils/inventoryUtils';

// 生成盘点任务编号
function generateTaskNo(): string {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const time = String(now.getTime()).slice(-6);
  return `ST${year}${month}${day}${time}`;
}

// 获取盘点任务列表
export async function getStocktakingTasks(req: Request, res: Response) {
  try {
    const { page = 1, limit = 10, search = '', status = '' } = req.query;
    const offset = (Number(page) - 1) * Number(limit);
    
    const db = getDatabase();
    
    // 构建查询条件
    let whereClause = "WHERE 1=1";
    const params: any[] = [];
    
    if (search) {
      whereClause += " AND (st.task_no LIKE ? OR st.title LIKE ?)";
      const searchPattern = `%${search}%`;
      params.push(searchPattern, searchPattern);
    }
    
    if (status) {
      whereClause += " AND st.status = ?";
      params.push(status);
    }
    
    // 获取总数
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM stocktaking_tasks st
      ${whereClause}
    `;
    const totalResult = await new Promise<{ total: number }>((resolve, reject) => {
      db.get(countQuery, params, (err, row) => {
        if (err) reject(err);
        else resolve(row as { total: number });
      });
    });
    
    // 获取数据
    const dataQuery = `
      SELECT 
        st.*,
        u.username as created_by_name
      FROM stocktaking_tasks st
      LEFT JOIN users u ON st.created_by = u.id
      ${whereClause}
      ORDER BY st.created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    const tasks = await new Promise<StocktakingTask[]>((resolve, reject) => {
      db.all(dataQuery, [...params, Number(limit), offset], (err, rows) => {
        if (err) reject(err);
        else resolve(rows as StocktakingTask[]);
      });
    });
    
    const response: PaginatedResponse<StocktakingTask> = {
      data: tasks,
      total: totalResult.total,
      page: Number(page),
      limit: Number(limit),
      totalPages: Math.ceil(totalResult.total / Number(limit))
    };
    
    res.json({
      success: true,
      message: '获取盘点任务列表成功',
      data: response
    });
    
  } catch (error) {
    console.error('获取盘点任务列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取盘点任务列表失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
}

// 获取单个盘点任务详情
export async function getStocktakingTask(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const db = getDatabase();
    
    // 获取盘点任务基本信息
    const taskQuery = `
      SELECT 
        st.*,
        u.username as created_by_name
      FROM stocktaking_tasks st
      LEFT JOIN users u ON st.created_by = u.id
      WHERE st.id = ?
    `;
    
    const task = await new Promise<StocktakingTask>((resolve, reject) => {
      db.get(taskQuery, [id], (err, row) => {
        if (err) reject(err);
        else resolve(row as StocktakingTask);
      });
    });
    
    if (!task) {
      return res.status(404).json({
        success: false,
        message: '盘点任务不存在'
      } as ApiResponse);
    }
    
    // 获取盘点明细
    const itemsQuery = `
      SELECT 
        si.*,
        CASE 
          WHEN si.item_type = 'material' THEN m.code
          WHEN si.item_type = 'product' THEN p.code
        END as item_code,
        CASE 
          WHEN si.item_type = 'material' THEN m.name
          WHEN si.item_type = 'product' THEN p.name
        END as item_name,
        CASE 
          WHEN si.item_type = 'material' THEN m.unit
          WHEN si.item_type = 'product' THEN p.unit
        END as item_unit,
        cu.username as counted_by_name,
        au.username as adjusted_by_name
      FROM stocktaking_items si
      LEFT JOIN materials m ON si.item_type = 'material' AND si.item_id = m.id
      LEFT JOIN products p ON si.item_type = 'product' AND si.item_id = p.id
      LEFT JOIN users cu ON si.counted_by = cu.id
      LEFT JOIN users au ON si.adjusted_by = au.id
      WHERE si.stocktaking_task_id = ?
      ORDER BY si.item_type, item_code
    `;
    
    const items = await new Promise<StocktakingItem[]>((resolve, reject) => {
      db.all(itemsQuery, [id], (err, rows) => {
        if (err) reject(err);
        else resolve(rows as StocktakingItem[]);
      });
    });
    
    res.json({
      success: true,
      message: '获取盘点任务详情成功',
      data: {
        task,
        items
      }
    });
    
  } catch (error) {
    console.error('获取盘点任务详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取盘点任务详情失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
}

// 创建盘点任务
export async function createStocktakingTask(req: Request, res: Response) {
  try {
    const {
      title,
      description = '',
      task_date,
      item_types = ['material', 'product'] // 默认盘点所有类型
    }: StocktakingTaskCreateInput & { item_types?: ('material' | 'product')[] } = req.body;
    
    // 验证必填字段
    if (!title || !task_date) {
      return res.status(400).json({
        success: false,
        message: '任务标题和盘点日期不能为空'
      } as ApiResponse);
    }
    
    const db = getDatabase();
    const taskNo = generateTaskNo();
    const userId = (req as any).user?.id;
    
    // 开始事务
    await new Promise<void>((resolve, reject) => {
      db.serialize(async () => {
        try {
          db.run('BEGIN TRANSACTION');
          
          // 创建盘点任务
          const taskId = await new Promise<number>((resolve, reject) => {
            db.run(`
              INSERT INTO stocktaking_tasks (
                task_no, title, description, task_date, created_by
              ) VALUES (?, ?, ?, ?, ?)
            `, [taskNo, title, description, task_date, userId], 
            function(err) {
              if (err) reject(err);
              else resolve(this.lastID);
            });
          });
          
          // 创建盘点明细（自动添加所有库存项目）
          for (const itemType of item_types) {
            const tableName = itemType === 'material' ? 'materials' : 'products';
            const itemsQuery = `
              SELECT id, current_stock 
              FROM ${tableName} 
              WHERE status = 'active'
            `;
            
            const items = await new Promise<any[]>((resolve, reject) => {
              db.all(itemsQuery, [], (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
              });
            });
            
            for (const item of items) {
              await new Promise<void>((resolve, reject) => {
                db.run(`
                  INSERT INTO stocktaking_items (
                    stocktaking_task_id, item_type, item_id, system_quantity
                  ) VALUES (?, ?, ?, ?)
                `, [taskId, itemType, item.id, item.current_stock],
                (err) => {
                  if (err) reject(err);
                  else resolve();
                });
              });
            }
          }
          
          db.run('COMMIT', (err) => {
            if (err) reject(err);
            else resolve();
          });
          
        } catch (error) {
          db.run('ROLLBACK');
          reject(error);
        }
      });
    });
    
    res.status(201).json({
      success: true,
      message: '盘点任务创建成功',
      data: { task_no: taskNo }
    } as ApiResponse);
    
  } catch (error) {
    console.error('创建盘点任务失败:', error);
    res.status(500).json({
      success: false,
      message: '创建盘点任务失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
}

// 更新盘点任务
export async function updateStocktakingTask(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const updateData: StocktakingTaskUpdateInput = req.body;
    
    const db = getDatabase();
    
    // 检查任务是否存在
    const existingTask = await new Promise<any>((resolve, reject) => {
      db.get('SELECT * FROM stocktaking_tasks WHERE id = ?', [id], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });
    
    if (!existingTask) {
      return res.status(404).json({
        success: false,
        message: '盘点任务不存在'
      } as ApiResponse);
    }
    
    // 构建更新SQL
    const updateFields: string[] = [];
    const params: any[] = [];
    
    if (updateData.title !== undefined) {
      updateFields.push('title = ?');
      params.push(updateData.title);
    }
    
    if (updateData.description !== undefined) {
      updateFields.push('description = ?');
      params.push(updateData.description);
    }
    
    if (updateData.task_date !== undefined) {
      updateFields.push('task_date = ?');
      params.push(updateData.task_date);
    }
    
    if (updateData.status !== undefined) {
      updateFields.push('status = ?');
      params.push(updateData.status);
      
      if (updateData.status === 'completed') {
        updateFields.push('completed_at = CURRENT_TIMESTAMP');
      }
    }
    
    if (updateFields.length === 0) {
      return res.status(400).json({
        success: false,
        message: '没有提供要更新的字段'
      } as ApiResponse);
    }
    
    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    params.push(id);
    
    const sql = `
      UPDATE stocktaking_tasks 
      SET ${updateFields.join(', ')}
      WHERE id = ?
    `;
    
    await new Promise<void>((resolve, reject) => {
      db.run(sql, params, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });
    
    res.json({
      success: true,
      message: '盘点任务更新成功'
    } as ApiResponse);

  } catch (error) {
    console.error('更新盘点任务失败:', error);
    res.status(500).json({
      success: false,
      message: '更新盘点任务失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
}

// 更新盘点明细
export async function updateStocktakingItem(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const updateData: StocktakingItemUpdateInput = req.body;
    const userId = (req as any).user?.id;

    const db = getDatabase();

    // 检查盘点明细是否存在
    const existingItem = await new Promise<any>((resolve, reject) => {
      db.get('SELECT * FROM stocktaking_items WHERE id = ?', [id], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!existingItem) {
      return res.status(404).json({
        success: false,
        message: '盘点明细不存在'
      } as ApiResponse);
    }

    // 构建更新SQL
    const updateFields: string[] = [];
    const params: any[] = [];

    if (updateData.actual_quantity !== undefined) {
      updateFields.push('actual_quantity = ?');
      params.push(updateData.actual_quantity);

      // 计算差异数量
      const differenceQuantity = updateData.actual_quantity - existingItem.system_quantity;
      updateFields.push('difference_quantity = ?');
      params.push(differenceQuantity);

      updateFields.push('counted_at = CURRENT_TIMESTAMP');
      updateFields.push('counted_by = ?');
      params.push(userId);

      updateFields.push('status = ?');
      params.push('counted');
    }

    if (updateData.status !== undefined) {
      updateFields.push('status = ?');
      params.push(updateData.status);

      if (updateData.status === 'adjusted') {
        updateFields.push('adjusted_at = CURRENT_TIMESTAMP');
        updateFields.push('adjusted_by = ?');
        params.push(userId);
      }
    }

    if (updateData.remark !== undefined) {
      updateFields.push('remark = ?');
      params.push(updateData.remark);
    }

    if (updateFields.length === 0) {
      return res.status(400).json({
        success: false,
        message: '没有提供要更新的字段'
      } as ApiResponse);
    }

    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    params.push(id);

    const sql = `
      UPDATE stocktaking_items
      SET ${updateFields.join(', ')}
      WHERE id = ?
    `;

    await new Promise<void>((resolve, reject) => {
      db.run(sql, params, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    res.json({
      success: true,
      message: '盘点明细更新成功'
    } as ApiResponse);

  } catch (error) {
    console.error('更新盘点明细失败:', error);
    res.status(500).json({
      success: false,
      message: '更新盘点明细失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
}

// 盘点差异分析
export async function getStocktakingAnalysis(req: Request, res: Response) {
  try {
    const { taskId } = req.params;
    const db = getDatabase();

    // 检查盘点任务是否存在
    const task = await new Promise<any>((resolve, reject) => {
      db.get('SELECT * FROM stocktaking_tasks WHERE id = ?', [taskId], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!task) {
      return res.status(404).json({
        success: false,
        message: '盘点任务不存在'
      } as ApiResponse);
    }

    // 获取盘点差异统计
    const analysisQuery = `
      SELECT
        COUNT(*) as total_items,
        COUNT(CASE WHEN difference_quantity > 0 THEN 1 END) as surplus_items,
        COUNT(CASE WHEN difference_quantity < 0 THEN 1 END) as shortage_items,
        COUNT(CASE WHEN difference_quantity = 0 THEN 1 END) as normal_items,
        SUM(CASE WHEN difference_quantity > 0 THEN difference_quantity ELSE 0 END) as total_surplus,
        SUM(CASE WHEN difference_quantity < 0 THEN ABS(difference_quantity) ELSE 0 END) as total_shortage,
        SUM(CASE WHEN difference_quantity > 0 THEN difference_quantity * system_quantity * 0.1 ELSE 0 END) as surplus_value,
        SUM(CASE WHEN difference_quantity < 0 THEN ABS(difference_quantity) * system_quantity * 0.1 ELSE 0 END) as shortage_value
      FROM stocktaking_items
      WHERE stocktaking_task_id = ? AND status = 'counted'
    `;

    const analysis = await new Promise<any>((resolve, reject) => {
      db.get(analysisQuery, [taskId], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    // 获取差异明细（按差异量排序）
    const detailsQuery = `
      SELECT
        si.*,
        CASE
          WHEN si.item_type = 'material' THEN m.code
          WHEN si.item_type = 'product' THEN p.code
        END as item_code,
        CASE
          WHEN si.item_type = 'material' THEN m.name
          WHEN si.item_type = 'product' THEN p.name
        END as item_name,
        CASE
          WHEN si.item_type = 'material' THEN m.unit
          WHEN si.item_type = 'product' THEN p.unit
        END as item_unit
      FROM stocktaking_items si
      LEFT JOIN materials m ON si.item_type = 'material' AND si.item_id = m.id
      LEFT JOIN products p ON si.item_type = 'product' AND si.item_id = p.id
      WHERE si.stocktaking_task_id = ? AND si.status = 'counted' AND si.difference_quantity != 0
      ORDER BY ABS(si.difference_quantity) DESC
      LIMIT 20
    `;

    const details = await new Promise<any[]>((resolve, reject) => {
      db.all(detailsQuery, [taskId], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    res.json({
      success: true,
      message: '获取盘点差异分析成功',
      data: {
        task_info: task,
        analysis: analysis,
        top_differences: details
      }
    } as ApiResponse);

  } catch (error) {
    console.error('获取盘点差异分析失败:', error);
    res.status(500).json({
      success: false,
      message: '获取盘点差异分析失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
}

// 库存调整（根据盘点结果）
export async function adjustInventory(req: Request, res: Response) {
  try {
    const { taskId } = req.params;
    const { itemIds } = req.body; // 要调整的盘点明细ID数组
    const userId = (req as any).user?.id;

    const db = getDatabase();

    // 检查盘点任务是否存在
    const task = await new Promise<any>((resolve, reject) => {
      db.get('SELECT * FROM stocktaking_tasks WHERE id = ?', [taskId], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!task) {
      return res.status(404).json({
        success: false,
        message: '盘点任务不存在'
      } as ApiResponse);
    }

    // 获取要调整的盘点明细
    let whereClause = 'WHERE stocktaking_task_id = ? AND status = ?';
    let params = [taskId, 'counted'];

    if (itemIds && itemIds.length > 0) {
      const placeholders = itemIds.map(() => '?').join(',');
      whereClause += ` AND id IN (${placeholders})`;
      params.push(...itemIds);
    }

    const itemsQuery = `
      SELECT * FROM stocktaking_items
      ${whereClause}
      AND difference_quantity != 0
    `;

    const items = await new Promise<any[]>((resolve, reject) => {
      db.all(itemsQuery, params, (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    if (items.length === 0) {
      return res.status(400).json({
        success: false,
        message: '没有需要调整的库存项目'
      } as ApiResponse);
    }

    // 开始事务进行库存调整
    await new Promise<void>((resolve, reject) => {
      db.serialize(async () => {
        try {
          db.run('BEGIN TRANSACTION');

          for (const item of items) {
            // 更新库存并记录变动
            await updateInventoryWithMovement(
              item.item_type,
              item.item_id,
              item.actual_quantity, // 目标库存数量
              'adjust',
              'stocktaking',
              parseInt(taskId),
              task.task_no,
              `盘点调整：差异${item.difference_quantity}`,
              userId
            );

            // 更新盘点明细状态
            await new Promise<void>((resolve, reject) => {
              db.run(`
                UPDATE stocktaking_items
                SET status = 'adjusted', adjusted_at = CURRENT_TIMESTAMP, adjusted_by = ?
                WHERE id = ?
              `, [userId, item.id], (err) => {
                if (err) reject(err);
                else resolve();
              });
            });
          }

          db.run('COMMIT', (err) => {
            if (err) reject(err);
            else resolve();
          });

        } catch (error) {
          db.run('ROLLBACK');
          reject(error);
        }
      });
    });

    res.json({
      success: true,
      message: `成功调整${items.length}个库存项目`,
      data: { adjustedCount: items.length }
    } as ApiResponse);

  } catch (error) {
    console.error('库存调整失败:', error);
    res.status(500).json({
      success: false,
      message: '库存调整失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
}
