"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getPurchaseSummaryReport = getPurchaseSummaryReport;
exports.getSalesSummaryReport = getSalesSummaryReport;
exports.getInventoryMovementReport = getInventoryMovementReport;
exports.getProductionCostReport = getProductionCostReport;
exports.getFinancialSummaryReport = getFinancialSummaryReport;
const database_1 = require("../models/database");
// 报表缓存机制
const reportCache = new Map();
const REPORT_CACHE_TTL = 10 * 60 * 1000; // 10分钟缓存
function getReportCacheKey(prefix, params) {
    return `${prefix}:${JSON.stringify(params)}`;
}
function getFromReportCache(key) {
    const cached = reportCache.get(key);
    if (cached && Date.now() - cached.timestamp < REPORT_CACHE_TTL) {
        return cached.data;
    }
    reportCache.delete(key);
    return null;
}
function setReportCache(key, data) {
    reportCache.set(key, { data, timestamp: Date.now() });
}
// 获取采购汇总报表
async function getPurchaseSummaryReport(req, res) {
    try {
        const { start_date, end_date } = req.query;
        if (!start_date || !end_date) {
            return res.status(400).json({
                success: false,
                message: '请提供开始日期和结束日期'
            });
        }
        // 检查缓存
        const cacheKey = getReportCacheKey('purchase_summary', { start_date, end_date });
        const cachedResult = getFromReportCache(cacheKey);
        if (cachedResult) {
            return res.json(cachedResult);
        }
        const db = (0, database_1.getDatabase)();
        // 获取采购汇总数据
        const summaryQuery = `
      SELECT 
        COUNT(DISTINCT pr.id) as total_orders,
        COUNT(DISTINCT pr.supplier_id) as supplier_count,
        COALESCE(SUM(pri.quantity * pri.unit_price), 0) as total_amount,
        COALESCE(SUM(pri.quantity), 0) as total_quantity
      FROM purchase_receipts pr
      LEFT JOIN purchase_receipt_items pri ON pr.id = pri.purchase_receipt_id
      WHERE pr.receipt_date BETWEEN ? AND ?
    `;
        const summary = await new Promise((resolve, reject) => {
            db.get(summaryQuery, [start_date, end_date], (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        // 获取采购明细数据
        const itemsQuery = `
      SELECT 
        m.id as material_id,
        m.code as material_code,
        m.name as material_name,
        m.unit,
        SUM(pri.quantity) as quantity,
        SUM(pri.quantity * pri.unit_price) as amount
      FROM purchase_receipts pr
      JOIN purchase_receipt_items pri ON pr.id = pri.purchase_receipt_id
      JOIN materials m ON pri.material_id = m.id
      WHERE pr.receipt_date BETWEEN ? AND ?
      GROUP BY m.id, m.code, m.name, m.unit
      ORDER BY amount DESC
    `;
        const items = await new Promise((resolve, reject) => {
            db.all(itemsQuery, [start_date, end_date], (err, rows) => {
                if (err)
                    reject(err);
                else
                    resolve(rows);
            });
        });
        const report = {
            period: `${start_date} 至 ${end_date}`,
            total_orders: summary.total_orders || 0,
            total_amount: summary.total_amount || 0,
            total_quantity: summary.total_quantity || 0,
            supplier_count: summary.supplier_count || 0,
            items: items
        };
        res.json({
            success: true,
            message: '获取采购汇总报表成功',
            data: report
        });
    }
    catch (error) {
        console.error('获取采购汇总报表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取采购汇总报表失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
}
// 获取销售汇总报表
async function getSalesSummaryReport(req, res) {
    try {
        const { start_date, end_date } = req.query;
        if (!start_date || !end_date) {
            return res.status(400).json({
                success: false,
                message: '请提供开始日期和结束日期'
            });
        }
        const db = (0, database_1.getDatabase)();
        // 获取销售汇总数据
        const summaryQuery = `
      SELECT 
        COUNT(DISTINCT sd.id) as total_orders,
        COUNT(DISTINCT sd.customer_id) as customer_count,
        COALESCE(SUM(sdi.quantity * sdi.unit_price), 0) as total_amount,
        COALESCE(SUM(sdi.quantity), 0) as total_quantity
      FROM sales_deliveries sd
      LEFT JOIN sales_delivery_items sdi ON sd.id = sdi.sales_delivery_id
      WHERE sd.delivery_date BETWEEN ? AND ?
    `;
        const summary = await new Promise((resolve, reject) => {
            db.get(summaryQuery, [start_date, end_date], (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        // 获取销售明细数据
        const itemsQuery = `
      SELECT 
        p.id as product_id,
        p.code as product_code,
        p.name as product_name,
        p.unit,
        SUM(sdi.quantity) as quantity,
        SUM(sdi.quantity * sdi.unit_price) as amount
      FROM sales_deliveries sd
      JOIN sales_delivery_items sdi ON sd.id = sdi.sales_delivery_id
      JOIN products p ON sdi.product_id = p.id
      WHERE sd.delivery_date BETWEEN ? AND ?
      GROUP BY p.id, p.code, p.name, p.unit
      ORDER BY amount DESC
    `;
        const items = await new Promise((resolve, reject) => {
            db.all(itemsQuery, [start_date, end_date], (err, rows) => {
                if (err)
                    reject(err);
                else
                    resolve(rows);
            });
        });
        const report = {
            period: `${start_date} 至 ${end_date}`,
            total_orders: summary.total_orders || 0,
            total_amount: summary.total_amount || 0,
            total_quantity: summary.total_quantity || 0,
            customer_count: summary.customer_count || 0,
            items: items
        };
        res.json({
            success: true,
            message: '获取销售汇总报表成功',
            data: report
        });
    }
    catch (error) {
        console.error('获取销售汇总报表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取销售汇总报表失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
}
// 获取库存变动报表
async function getInventoryMovementReport(req, res) {
    try {
        const { start_date, end_date, item_type } = req.query;
        if (!start_date || !end_date) {
            return res.status(400).json({
                success: false,
                message: '请提供开始日期和结束日期'
            });
        }
        const db = (0, database_1.getDatabase)();
        // 构建查询条件
        let whereClause = 'WHERE im.created_at BETWEEN ? AND ?';
        const params = [start_date + ' 00:00:00', end_date + ' 23:59:59'];
        if (item_type) {
            whereClause += ' AND im.item_type = ?';
            params.push(item_type);
        }
        // 获取变动汇总数据
        const summaryQuery = `
      SELECT 
        COUNT(*) as total_movements,
        COUNT(CASE WHEN movement_type = 'in' THEN 1 END) as in_movements,
        COUNT(CASE WHEN movement_type = 'out' THEN 1 END) as out_movements,
        COUNT(CASE WHEN movement_type = 'adjust' THEN 1 END) as adjust_movements
      FROM inventory_movements im
      ${whereClause}
    `;
        const summary = await new Promise((resolve, reject) => {
            db.get(summaryQuery, params, (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        // 获取变动明细数据
        const itemsQuery = `
      SELECT 
        im.item_id,
        im.item_type,
        CASE 
          WHEN im.item_type = 'material' THEN m.code
          WHEN im.item_type = 'product' THEN p.code
        END as item_code,
        CASE 
          WHEN im.item_type = 'material' THEN m.name
          WHEN im.item_type = 'product' THEN p.name
        END as item_name,
        CASE 
          WHEN im.item_type = 'material' THEN m.unit
          WHEN im.item_type = 'product' THEN p.unit
        END as unit,
        SUM(CASE WHEN im.movement_type = 'in' THEN im.quantity ELSE 0 END) as in_quantity,
        SUM(CASE WHEN im.movement_type = 'out' THEN im.quantity ELSE 0 END) as out_quantity,
        SUM(CASE WHEN im.movement_type = 'adjust' THEN im.quantity ELSE 0 END) as adjust_quantity,
        SUM(CASE 
          WHEN im.movement_type = 'in' THEN im.quantity
          WHEN im.movement_type = 'out' THEN -im.quantity
          ELSE im.quantity
        END) as net_change
      FROM inventory_movements im
      LEFT JOIN materials m ON im.item_type = 'material' AND im.item_id = m.id
      LEFT JOIN products p ON im.item_type = 'product' AND im.item_id = p.id
      ${whereClause}
      GROUP BY im.item_id, im.item_type, item_code, item_name, unit
      ORDER BY ABS(net_change) DESC
    `;
        const items = await new Promise((resolve, reject) => {
            db.all(itemsQuery, params, (err, rows) => {
                if (err)
                    reject(err);
                else
                    resolve(rows);
            });
        });
        const report = {
            period: `${start_date} 至 ${end_date}`,
            total_movements: summary.total_movements || 0,
            in_movements: summary.in_movements || 0,
            out_movements: summary.out_movements || 0,
            adjust_movements: summary.adjust_movements || 0,
            items: items
        };
        res.json({
            success: true,
            message: '获取库存变动报表成功',
            data: report
        });
    }
    catch (error) {
        console.error('获取库存变动报表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取库存变动报表失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
}
// 获取生产成本报表
async function getProductionCostReport(req, res) {
    try {
        const { start_date, end_date } = req.query;
        if (!start_date || !end_date) {
            return res.status(400).json({
                success: false,
                message: '请提供开始日期和结束日期'
            });
        }
        const db = (0, database_1.getDatabase)();
        // 获取生产汇总数据
        const summaryQuery = `
      SELECT
        COUNT(DISTINCT pc.id) as total_productions,
        COALESCE(SUM(pci.consumed_quantity * m.cost_price), 0) as total_cost,
        COALESCE(SUM(pc.completed_quantity * p.cost_price), 0) as total_output_value
      FROM production_completions pc
      LEFT JOIN production_completion_items pci ON pc.id = pci.production_completion_id
      LEFT JOIN materials m ON pci.material_id = m.id
      LEFT JOIN production_plans pp ON pc.production_plan_id = pp.id
      LEFT JOIN products p ON pp.product_id = p.id
      WHERE pc.completion_date BETWEEN ? AND ?
    `;
        const summary = await new Promise((resolve, reject) => {
            db.get(summaryQuery, [start_date, end_date], (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        // 获取生产明细数据
        const itemsQuery = `
      SELECT
        p.id as product_id,
        p.code as product_code,
        p.name as product_name,
        p.unit,
        SUM(pc.completed_quantity) as produced_quantity,
        SUM(pci.consumed_quantity * m.cost_price) as material_cost,
        SUM(pc.completed_quantity * p.cost_price) as output_value,
        CASE
          WHEN SUM(pc.completed_quantity) > 0
          THEN SUM(pci.consumed_quantity * m.cost_price) / SUM(pc.completed_quantity)
          ELSE 0
        END as unit_cost
      FROM production_completions pc
      JOIN production_plans pp ON pc.production_plan_id = pp.id
      JOIN products p ON pp.product_id = p.id
      LEFT JOIN production_completion_items pci ON pc.id = pci.production_completion_id
      LEFT JOIN materials m ON pci.material_id = m.id
      WHERE pc.completion_date BETWEEN ? AND ?
      GROUP BY p.id, p.code, p.name, p.unit
      ORDER BY material_cost DESC
    `;
        const items = await new Promise((resolve, reject) => {
            db.all(itemsQuery, [start_date, end_date], (err, rows) => {
                if (err)
                    reject(err);
                else
                    resolve(rows);
            });
        });
        const totalCost = summary.total_cost || 0;
        const totalOutputValue = summary.total_output_value || 0;
        const profitMargin = totalOutputValue > 0 ? ((totalOutputValue - totalCost) / totalOutputValue * 100) : 0;
        const report = {
            period: `${start_date} 至 ${end_date}`,
            total_productions: summary.total_productions || 0,
            total_cost: totalCost,
            total_output_value: totalOutputValue,
            profit_margin: profitMargin,
            items: items
        };
        res.json({
            success: true,
            message: '获取生产成本报表成功',
            data: report
        });
    }
    catch (error) {
        console.error('获取生产成本报表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取生产成本报表失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
}
// 获取财务汇总报表
async function getFinancialSummaryReport(req, res) {
    try {
        const { start_date, end_date } = req.query;
        if (!start_date || !end_date) {
            return res.status(400).json({
                success: false,
                message: '请提供开始日期和结束日期'
            });
        }
        const db = (0, database_1.getDatabase)();
        // 获取采购金额
        const purchaseQuery = `
      SELECT COALESCE(SUM(pri.quantity * pri.unit_price), 0) as purchase_amount
      FROM purchase_receipts pr
      JOIN purchase_receipt_items pri ON pr.id = pri.purchase_receipt_id
      WHERE pr.receipt_date BETWEEN ? AND ?
    `;
        const purchaseResult = await new Promise((resolve, reject) => {
            db.get(purchaseQuery, [start_date, end_date], (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        // 获取销售金额
        const salesQuery = `
      SELECT COALESCE(SUM(sdi.quantity * sdi.unit_price), 0) as sales_amount
      FROM sales_deliveries sd
      JOIN sales_delivery_items sdi ON sd.id = sdi.sales_delivery_id
      WHERE sd.delivery_date BETWEEN ? AND ?
    `;
        const salesResult = await new Promise((resolve, reject) => {
            db.get(salesQuery, [start_date, end_date], (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        // 获取生产成本
        const productionCostQuery = `
      SELECT COALESCE(SUM(pci.consumed_quantity * m.cost_price), 0) as production_cost
      FROM production_completions pc
      JOIN production_completion_items pci ON pc.id = pci.production_completion_id
      JOIN materials m ON pci.material_id = m.id
      WHERE pc.completion_date BETWEEN ? AND ?
    `;
        const productionCostResult = await new Promise((resolve, reject) => {
            db.get(productionCostQuery, [start_date, end_date], (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        // 获取当前库存价值
        const inventoryValueQuery = `
      SELECT
        COALESCE(SUM(m.current_stock * m.cost_price), 0) +
        COALESCE(SUM(p.current_stock * p.cost_price), 0) as inventory_value
      FROM materials m, products p
      WHERE m.status = 'active' AND p.status = 'active'
    `;
        const inventoryValueResult = await new Promise((resolve, reject) => {
            db.get(inventoryValueQuery, [], (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        // 获取月度数据
        const monthlyDataQuery = `
      SELECT
        strftime('%Y-%m', date) as month,
        SUM(CASE WHEN type = 'purchase' THEN amount ELSE 0 END) as purchase_amount,
        SUM(CASE WHEN type = 'sales' THEN amount ELSE 0 END) as sales_amount,
        SUM(CASE WHEN type = 'sales' THEN amount ELSE 0 END) -
        SUM(CASE WHEN type = 'purchase' THEN amount ELSE 0 END) as profit
      FROM (
        SELECT pr.receipt_date as date, 'purchase' as type, SUM(pri.quantity * pri.unit_price) as amount
        FROM purchase_receipts pr
        JOIN purchase_receipt_items pri ON pr.id = pri.purchase_receipt_id
        WHERE pr.receipt_date BETWEEN ? AND ?
        GROUP BY pr.receipt_date

        UNION ALL

        SELECT sd.delivery_date as date, 'sales' as type, SUM(sdi.quantity * sdi.unit_price) as amount
        FROM sales_deliveries sd
        JOIN sales_delivery_items sdi ON sd.id = sdi.sales_delivery_id
        WHERE sd.delivery_date BETWEEN ? AND ?
        GROUP BY sd.delivery_date
      ) combined
      GROUP BY strftime('%Y-%m', date)
      ORDER BY month
    `;
        const monthlyData = await new Promise((resolve, reject) => {
            db.all(monthlyDataQuery, [start_date, end_date, start_date, end_date], (err, rows) => {
                if (err)
                    reject(err);
                else
                    resolve(rows);
            });
        });
        const purchaseAmount = purchaseResult.purchase_amount || 0;
        const salesAmount = salesResult.sales_amount || 0;
        const productionCost = productionCostResult.production_cost || 0;
        const grossProfit = salesAmount - purchaseAmount - productionCost;
        const profitMargin = salesAmount > 0 ? (grossProfit / salesAmount * 100) : 0;
        const report = {
            period: `${start_date} 至 ${end_date}`,
            purchase_amount: purchaseAmount,
            sales_amount: salesAmount,
            production_cost: productionCost,
            gross_profit: grossProfit,
            profit_margin: profitMargin,
            inventory_value: inventoryValueResult.inventory_value || 0,
            monthly_data: monthlyData
        };
        res.json({
            success: true,
            message: '获取财务汇总报表成功',
            data: report
        });
    }
    catch (error) {
        console.error('获取财务汇总报表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取财务汇总报表失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
}
//# sourceMappingURL=reportsController.js.map