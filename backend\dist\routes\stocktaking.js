"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const stocktakingController_1 = require("../controllers/stocktakingController");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
// 所有路由都需要认证
router.use(auth_1.authenticateToken);
// GET /api/stocktaking - 获取盘点任务列表
router.get('/', stocktakingController_1.getStocktakingTasks);
// GET /api/stocktaking/:id - 获取单个盘点任务详情
router.get('/:id', stocktakingController_1.getStocktakingTask);
// POST /api/stocktaking - 创建盘点任务
router.post('/', stocktakingController_1.createStocktakingTask);
// PUT /api/stocktaking/:id - 更新盘点任务
router.put('/:id', stocktakingController_1.updateStocktakingTask);
// PUT /api/stocktaking/items/:id - 更新盘点明细
router.put('/items/:id', stocktakingController_1.updateStocktakingItem);
// GET /api/stocktaking/:taskId/analysis - 获取盘点差异分析
router.get('/:taskId/analysis', stocktakingController_1.getStocktakingAnalysis);
// POST /api/stocktaking/:taskId/adjust - 库存调整
router.post('/:taskId/adjust', stocktakingController_1.adjustInventory);
exports.default = router;
//# sourceMappingURL=stocktaking.js.map