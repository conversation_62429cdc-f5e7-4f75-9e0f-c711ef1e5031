import express from 'express';
import {
  getStocktakingTasks,
  getStocktakingTask,
  createStocktakingTask,
  updateStocktakingTask,
  updateStocktakingItem,
  getStocktakingAnalysis,
  adjustInventory
} from '../controllers/stocktakingController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// 所有路由都需要认证
router.use(authenticateToken);

// GET /api/stocktaking - 获取盘点任务列表
router.get('/', getStocktakingTasks);

// GET /api/stocktaking/:id - 获取单个盘点任务详情
router.get('/:id', getStocktakingTask);

// POST /api/stocktaking - 创建盘点任务
router.post('/', createStocktakingTask);

// PUT /api/stocktaking/:id - 更新盘点任务
router.put('/:id', updateStocktakingTask);

// PUT /api/stocktaking/items/:id - 更新盘点明细
router.put('/items/:id', updateStocktakingItem);

// GET /api/stocktaking/:taskId/analysis - 获取盘点差异分析
router.get('/:taskId/analysis', getStocktakingAnalysis);

// POST /api/stocktaking/:taskId/adjust - 库存调整
router.post('/:taskId/adjust', adjustInventory);

export default router;
